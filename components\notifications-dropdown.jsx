"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>,
  <PERSON>,
  Check,
  X,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "lucide-react"
import useStore from "@/lib/store"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import { motion, AnimatePresence } from "framer-motion"

export function NotificationsDropdown({ isOpen, onOpenChange }) {
  const dropdownRef = useRef(null)
  const scrollContainerRef = useRef(null)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [loading, setLoading] = useState(false)
  
  const {
    notifications,
    unreadCount,
    markNotificationAsRead,
    markAllNotificationsAsRead
  } = useStore()

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onOpenChange(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onOpenChange])

  const getStatusBadge = (statusId) => {
    switch (statusId) {
      case 1: return { color: "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300", label: "ინფო" }
      case 2: return { color: "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300", label: "გაფრთხილება" }
      case 3: return { color: "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300", label: "მნიშვნელოვანი" }
      default: return { color: "bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300", label: "ზოგადი" }
    }
  }

  const getNotificationTypeEmoji = (notification) => {
    if (notification.rate) return "⭐"
    if (notification.important) return "🔔"
    if (notification.status_id === 3) return "⚠️"
    if (notification.status_id === 2) return "💡"
    return "📢"
  }

  const handleMarkAsRead = async (notificationId) => {
    try {
      const response = await api.notifications.markAsRead(notificationId)
      if (response.success) {
        markNotificationAsRead(notificationId)
        toast.success("ნოტიფიკაცია მონიშნულია წაკითხულად")
      }
    } catch (error) {
      toast.error("შეცდომა ნოტიფიკაციის მონიშვნისას")
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      const response = await api.notifications.markAllAsRead()
      if (response.success) {
        markAllNotificationsAsRead()
        toast.success("ყველა ნოტიფიკაცია მონიშნულია წაკითხულად")
      }
    } catch (error) {
      toast.error("შეცდომა ნოტიფიკაციების მონიშვნისას")
    }
  }



  const loadMore = async () => {
    if (loading || !hasMore) return

    setLoading(true)
    try {
      const nextPage = page + 1
      const response = await api.notifications.getNotifications(nextPage)
      if (response.success && response.data?.data?.length > 0) {
        setPage(nextPage)
        // Add new notifications to existing ones
        const newNotifications = [...notifications, ...response.data.data]
        useStore.getState().setNotifications(newNotifications)
      } else {
        setHasMore(false)
      }
    } catch (error) {
      toast.error("შეცდომა ნოტიფიკაციების ჩატვირთვისას")
    } finally {
      setLoading(false)
    }
  }

  // Handle scroll for pagination
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target
    // Load more when user scrolls to within 100px of the bottom
    if (scrollHeight - scrollTop <= clientHeight + 100) {
      loadMore()
    }
  }

  const formatTime = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true
      })
    } catch {
      return "ახლახან"
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={dropdownRef}
          initial={{ opacity: 0, y: -10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.95 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className="fixed right-4 top-16 w-[420px] max-w-[calc(100vw-2rem)] z-[9999] notifications-dropdown"
          style={{ zIndex: 9999 }}
        >
          <Card className="shadow-xl border border-border/50 bg-background/95 backdrop-blur-md supports-[backdrop-filter]:bg-background/80">
            {/* Header */}
            <CardHeader className="pb-4 border-b border-border/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bell className="w-5 h-5 text-primary" />
                  <CardTitle className="text-lg font-semibold">ნოტიფიკაციები</CardTitle>
                </div>
                
              </div>
            </CardHeader>

            {/* Content */}
            <CardContent className="p-0">
              <div
                ref={scrollContainerRef}
                className="max-h-[500px] overflow-y-auto"
                onScroll={handleScroll}
              >
                {!notifications || notifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
                      <Bell className="w-8 h-8 text-muted-foreground/50" />
                    </div>
                    <h3 className="font-medium text-muted-foreground mb-1">ნოტიფიკაციები არ არის</h3>
                    <p className="text-sm text-muted-foreground/70">ყველა შეტყობინება წაკითხულია</p>
                  </div>
                ) : (
                  <div className="divide-y divide-border/50">
                    {(notifications || []).map((notification, index) => {
                      const statusBadge = getStatusBadge(notification.status_id)
                      const isUnread = !notification.read_at

                      return (
                        <motion.div
                          key={notification.id || `notification-${index}`}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className={`relative p-4 hover:bg-muted/30 transition-all duration-200 group ${
                            isUnread ? 'bg-primary/5' : ''
                          }`}
                        >
                          {/* Unread indicator */}
                          {isUnread && (
                            <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full" />
                          )}

                          <div className="flex items-start gap-3 pl-2">
                            {/* Icon and emoji */}
                            <div className="flex-shrink-0 relative">
                              <div className="w-10 h-10 rounded-full bg-background border border-border/50 flex items-center justify-center">
                                <span className="text-lg">{getNotificationTypeEmoji(notification)}</span>
                              </div>
                              {isUnread && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border-2 border-background" />
                              )}
                            </div>

                            <div className="flex-1 min-w-0">
                              {/* Header */}
                              <div className="flex items-start justify-between gap-2 mb-2">
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant="outline"
                                    className={`text-xs px-2 py-0.5 ${statusBadge.color} border-0`}
                                  >
                                    {statusBadge.label}
                                  </Badge>
                                  <span className="text-xs text-muted-foreground flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    {formatTime(notification.created_at)}
                                  </span>
                                </div>

                                {/* Action buttons */}
                                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                  {isUnread && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        handleMarkAsRead(notification.id)
                                      }}
                                      className="h-7 w-7 p-0 hover:bg-primary/10"
                                      title="წაკითხულად მონიშვნა"
                                    >
                                      <Check className="w-3.5 h-3.5" />
                                    </Button>
                                  )}
                                </div>
                              </div>

                              {/* Content */}
                              <p className={`text-sm leading-relaxed mb-3 ${
                                isUnread ? 'font-medium text-foreground' : 'text-muted-foreground'
                              }`}>
                                {notification.text}
                              </p>

                              {/* Rating display */}
                              {notification.rate && (
                                <div className="flex items-center gap-1 bg-yellow-50 dark:bg-yellow-900/20 rounded-md px-2 py-1 w-fit">
                                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                  <span className="text-sm font-medium text-yellow-700 dark:text-yellow-400">
                                    {notification.rate}/10 ქულა
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                )}

                {/* Loading indicator for scroll pagination */}
                {loading && hasMore && (
                  <div className="p-4 border-t border-border/50 flex justify-center">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                      იტვირთება...
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
