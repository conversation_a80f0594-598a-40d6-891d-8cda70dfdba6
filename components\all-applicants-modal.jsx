"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { 
  Loader2, 
  Users, 
  Search, 
  Filter, 
  ChevronLeft, 
  ChevronRight,
  User,
  Building,
  Calendar,
  Star,
  Eye,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react"

export function AllApplicantsModal({ isOpen, onOpenChange, onRefreshCount }) {
  const [applicants, setApplicants] = useState([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalApplicants, setTotalApplicants] = useState(0)
  const [perPage] = useState(12)
  
  // Filters
  const [statusFilter, setStatusFilter] = useState("all")
  const [nodeFilter, setNodeFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  
  // Available nodes for filter (will be populated from applicants data)
  const [availableNodes, setAvailableNodes] = useState([])

  // Fetch applicants
  const fetchApplicants = async (page = 1) => {
    setLoading(true)
    try {
      const response = await api.network.getAllApplicants(
        perPage,
        page,
        statusFilter === "all" ? null : statusFilter,
        nodeFilter === "all" ? null : nodeFilter
      )
      
      if (response && response.data) {
        let applicantsData = response.data || []
        
        // Filter by search term if provided
        if (searchTerm.trim()) {
          applicantsData = applicantsData.filter(applicant => {
            const name = applicant.user?.name || applicant.user?.email || ''
            const nodeName = applicant.node?.label || applicant.node?.name || ''
            return name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   nodeName.toLowerCase().includes(searchTerm.toLowerCase())
          })
        }
        
        setApplicants(applicantsData)
        
        // Extract unique nodes for filter dropdown
        const nodes = [...new Map(
          applicantsData
            .filter(app => app.node)
            .map(app => [app.node.id, app.node])
        ).values()]
        setAvailableNodes(nodes)
        
        // Set pagination
        const meta = response.meta || {}
        setCurrentPage(meta.current_page || 1)
        setTotalPages(meta.last_page || 1)
        setTotalApplicants(meta.total || 0)
      }
    } catch (error) {
      console.error('Error fetching applicants:', error)
      toast.error('აპლიკანტების ჩატვირთვა ვერ მოხერხდა')
    } finally {
      setLoading(false)
    }
  }

  // Fetch applicants when modal opens or filters change
  useEffect(() => {
    if (isOpen) {
      setCurrentPage(1)
      fetchApplicants(1)
    }
  }, [isOpen, statusFilter, nodeFilter])

  // Fetch when page changes
  useEffect(() => {
    if (isOpen && currentPage > 1) {
      fetchApplicants(currentPage)
    }
  }, [currentPage])

  // Search with debounce
  useEffect(() => {
    if (!isOpen) return
    
    const timeoutId = setTimeout(() => {
      setCurrentPage(1)
      fetchApplicants(1)
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  // Get status badge
  const getStatusBadge = (statusId) => {
    switch (statusId) {
      case 1:
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          <Clock className="w-3 h-3 mr-1" />
          ახალი
        </Badge>
      case 2:
        return <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          დადასტურებული
        </Badge>
      case 3:
        return <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
          <XCircle className="w-3 h-3 mr-1" />
          უარყოფილი
        </Badge>
      default:
        return <Badge variant="outline">უცნობი</Badge>
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('ka-GE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Clear filters
  const clearFilters = () => {
    setStatusFilter("all")
    setNodeFilter("all")
    setSearchTerm("")
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl w-full max-w-[95vw] h-[85vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-2xl font-bold text-center flex items-center justify-center gap-2">
            <Users className="w-6 h-6 text-blue-600" />
            ყველა აპლიკანტი ({totalApplicants})
          </DialogTitle>
        </DialogHeader>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 pb-4 border-b">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="ძებნა სახელით ან ნოუდით..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="სტატუსი" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">ყველა სტატუსი</SelectItem>
              <SelectItem value="1">ახალი</SelectItem>
              <SelectItem value="2">დადასტურებული</SelectItem>
              <SelectItem value="3">უარყოფილი</SelectItem>
            </SelectContent>
          </Select>

          <Select value={nodeFilter} onValueChange={setNodeFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="ნოუდი" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">ყველა ნოუდი</SelectItem>
              {availableNodes.map(node => (
                <SelectItem key={node.id} value={node.id.toString()}>
                  {node.label || node.name || `ნოუდი ${node.id}`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={clearFilters} className="whitespace-nowrap">
            <Filter className="w-4 h-4 mr-2" />
            გასუფთავება
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="w-8 h-8 animate-spin" />
            </div>
          ) : applicants.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <Users className="w-16 h-16 mb-4 opacity-50" />
              <p className="text-lg">აპლიკანტები არ მოიძებნა</p>
            </div>
          ) : (
            <div className="h-full overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-1">
                {applicants.map((applicant) => (
                  <Card key={applicant.id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium text-sm">
                            {applicant.user?.name || applicant.user?.email || 'უცნობი'}
                          </span>
                        </div>
                        {getStatusBadge(applicant.status_id)}
                      </div>

                      <div className="space-y-2 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Building className="w-3 h-3" />
                          <span>{applicant.node?.label || applicant.node?.name || 'უცნობი ნოუდი'}</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Star className="w-3 h-3" />
                          <span>რეიტინგი: {applicant.user?.rate || 'N/A'}/10</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(applicant.created_at)}</span>
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t">
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="w-3 h-3 mr-2" />
                          დეტალები
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              გვერდი {currentPage} / {totalPages} (სულ {totalApplicants})
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1 || loading}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              
              <span className="px-3 py-1 text-sm bg-muted rounded">
                {currentPage}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages || loading}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
